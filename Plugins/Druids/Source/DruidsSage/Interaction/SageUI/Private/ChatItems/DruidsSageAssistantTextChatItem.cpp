#include "ChatItems/DruidsSageAssistantTextChatItem.h"
#include <Dom/JsonObject.h>
#include <Serialization/JsonWriter.h>
#include <Serialization/JsonSerializer.h>

#include "DruidsSageMessagingHandler.h"
#include "MarkdownRichTextBlock.h"
#include "HAL/PlatformApplicationMisc.h"

UDruidsSageAssistantTextChatItem::UDruidsSageAssistantTextChatItem(const FObjectInitializer& ObjectInitializer)
	: Super(ObjectInitializer)
	, ChatText(TEXT(""))
	, MessageWidget(nullptr)
{
}

void UDruidsSageAssistantTextChatItem::NativePreConstruct()
{
	Super::NativePreConstruct();
	UpdateWidgetStyling();
}

void UDruidsSageAssistantTextChatItem::NativeConstruct()
{
	Super::NativeConstruct();
	UpdateWidgetStyling();
}

void UDruidsSageAssistantTextChatItem::SynchronizeProperties()
{
	Super::SynchronizeProperties();
	UpdateWidgetStyling();
}

FName UDruidsSageAssistantTextChatItem::GetTypeNameCpp() const
{
	return GetClassName();
}

void UDruidsSageAssistantTextChatItem::FillInDruidsMessageCpp(FDruidsSageChatMessage& Message) const
{
	Message.SetChatContent(ChatText);
}

EDruidsSageChatRole UDruidsSageAssistantTextChatItem::GetMessageRoleCpp() const
{
	return EDruidsSageChatRole::Assistant;
}

TWeakObjectPtr<UDruidsSageMessagingHandler> UDruidsSageAssistantTextChatItem::GetMessagingHandlerCpp() const
{
	return MessagingHandler;
}

void UDruidsSageAssistantTextChatItem::UpdateFromContentJsonCpp(const TSharedPtr<FJsonValue>& ContentJson)
{
	TSharedPtr<FJsonObject>* JsonObject = nullptr;
	if (!ContentJson.IsValid() || !ContentJson->TryGetObject(JsonObject) || !JsonObject)
	{
		return;
	}

	FString Type;
	(*JsonObject)->TryGetStringField(TEXT("type"), Type);
	if (Type == TEXT("text"))
	{
		FString Text;
		(*JsonObject)->TryGetStringField(TEXT("text"), Text);
		
		ChatText = Text;
		if (MessageWidget)
		{
			MessageWidget->SetNewText(FText::FromString(Text));
		}
	}
}

void UDruidsSageAssistantTextChatItem::InitializeAssistantTextChatItem(const FString& InChatText)
{
	ChatText = InChatText;

	UpdateWidgetStyling();
}

void UDruidsSageAssistantTextChatItem::SetScrollBoxReference(UScrollBox* InScrollBox)
{
	SetupMessagingHandler(InScrollBox);
}

void UDruidsSageAssistantTextChatItem::SetupMessagingHandler(UScrollBox* ScrollBox)
{
	MessagingHandler = NewObject<UDruidsSageMessagingHandler>();
	MessagingHandler->SetFlags(RF_Standalone);
	MessagingHandler->ScrollBoxReference = ScrollBox;

	MessagingHandler->OnMessageContentUpdated.BindLambda([this](FString Content)
	{
		if (!MessageWidget)
		{
			return;
		}

		ChatText = Content;
		MessageWidget->SetNewText(FText::FromString(Content));
	});
}

void UDruidsSageAssistantTextChatItem::UpdateWidgetStyling()
{
	if (!MessageWidget)
	{
		return;
	}

	MessageWidget->SetNewText(FText::FromString(ChatText));
}
