#pragma once

#include <CoreMinimal.h>

#include "DruidsSageChatTypes.h"
#include "IDruidsSageChatItem.h"

#include "DruidsSageAssistantTextChatItem.generated.h"

class UMarkdownRichTextBlock;
class UDruidsSageMessagingHandler;
class UTextBlock;
struct FDruidsSageExtensionDefinition;

UCLASS(BlueprintType, Blueprintable)
class SAGEUI_API UDruidsSageAssistantTextChatItem : public UIDruidsSageChatItem
{
	GENERATED_BODY()

public:
	UDruidsSageAssistantTextChatItem(const FObjectInitializer& ObjectInitializer);

	// UUserWidget interface
	virtual void NativePreConstruct() override;
	virtual void NativeConstruct() override;
	virtual void SynchronizeProperties() override;
	// End of UUserWidget interface

	// UIDruidsSageChatItem interface implementation
	virtual FName GetTypeNameCpp() const override;
	virtual void FillInDruidsMessageCpp(FDruidsSageChatMessage& Message) const override;
	virtual EDruidsSageChatRole GetMessageRoleCpp() const override;
	virtual TWeakObjectPtr<UDruidsSageMessagingHandler> GetMessagingHandlerCpp() const override;
	virtual void UpdateFromContentJsonCpp(const TSharedPtr<FJsonValue>& ContentJson) override;

	// Configuration properties
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Chat Item")
	FString ChatText;

	// Initialization methods
	void InitializeAssistantTextChatItem(const FString& InChatText);

	UFUNCTION(BlueprintCallable, Category = "Chat Item")
	void SetScrollBoxReference(class UScrollBox* InScrollBox);

	// Copy functionality - can be overridden in Blueprint
	UFUNCTION(BlueprintImplementableEvent, Category = "Chat Item")
	void OnCopyMarkdownButtonPressed();

	UFUNCTION(BlueprintImplementableEvent, Category = "Chat Item")
	void OnCopyPlainTextButtonPressed();

	// Copy methods that can be called from Blueprint or C++
	UFUNCTION(BlueprintCallable, Category = "Chat Item")
	void CopyMarkdownToClipboard();

	UFUNCTION(BlueprintCallable, Category = "Chat Item")
	void CopyPlainTextToClipboard();

	static FName GetClassName() { return "UDruidsSageAssistantTextChatItem"; }

protected:
	UPROPERTY(meta = (BindWidget), BlueprintReadOnly, Category = "Chat Item")
	UMarkdownRichTextBlock* MessageWidget;

private:
	TWeakObjectPtr<UDruidsSageMessagingHandler> MessagingHandler;

	void SetupMessagingHandler(class UScrollBox* ScrollBox);
	void UpdateWidgetStyling();
};
